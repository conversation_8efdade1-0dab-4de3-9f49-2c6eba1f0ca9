<template>
  <div class="service-withdraw">
    <TopNav title="提现管理" />

    <div class="content-container">
      <!-- 统计概览 -->
      <div class="stats-overview">
        <el-tabs v-model="activeStatsTab" class="stats-tabs">
          <el-tab-pane label="全量统计" name="overall">
            <el-row :gutter="20" class="stats-cards">
              <el-col :span="6">
                <el-card class="stat-card requested">
                  <div class="stat-content">
                    <div class="stat-value">{{ censusData.overall?.requested?.count || 0 }}</div>
                    <div class="stat-amount">¥{{ censusData.overall?.requested?.amount || '0.00' }}</div>
                    <div class="stat-label">申请汇总</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card class="stat-card completed">
                  <div class="stat-content">
                    <div class="stat-value">{{ censusData.overall?.completed?.count || 0 }}</div>
                    <div class="stat-amount">¥{{ censusData.overall?.completed?.amount || '0.00' }}</div>
                    <div class="stat-label">到账汇总</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card class="stat-card pending">
                  <div class="stat-content">
                    <div class="stat-value">{{ censusData.overall?.pending?.count || 0 }}</div>
                    <div class="stat-amount">¥{{ censusData.overall?.pending?.amount || '0.00' }}</div>
                    <div class="stat-label">待处理</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card class="stat-card failed">
                  <div class="stat-content">
                    <div class="stat-value">{{ censusData.overall?.failed?.count || 0 }}</div>
                    <div class="stat-amount">¥{{ censusData.overall?.failed?.amount || '0.00' }}</div>
                    <div class="stat-label">失败/关闭</div>
                  </div>
                </el-card>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="stats-cards mt-20">
              <el-col :span="8">
                <el-card class="stat-card audit">
                  <div class="stat-content">
                    <div class="audit-stats">
                      <div class="audit-item">
                        <span class="audit-label">通过:</span>
                        <span class="audit-value approved">{{ censusData.overall?.auditApproved || 0 }}</span>
                      </div>
                      <div class="audit-item">
                        <span class="audit-label">拒绝:</span>
                        <span class="audit-value rejected">{{ censusData.overall?.auditRejected || 0 }}</span>
                      </div>
                      <div class="audit-item">
                        <span class="audit-label">待审:</span>
                        <span class="audit-value pending">{{ censusData.overall?.auditPending || 0 }}</span>
                      </div>
                    </div>
                    <div class="stat-label">审核统计</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card class="stat-card fee">
                  <div class="stat-content">
                    <div class="stat-value">¥{{ censusData.overall?.feeAmount || '0.00' }}</div>
                    <div class="stat-label">手续费总额</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card class="stat-card review-time">
                  <div class="stat-content">
                    <div class="stat-value">
                      {{ censusData.overall?.avgReviewMinutes ? `${censusData.overall.avgReviewMinutes}分钟` : '暂无数据' }}
                    </div>
                    <div class="stat-label">平均审核耗时</div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </el-tab-pane>

          <el-tab-pane label="今日统计" name="today">
            <el-row :gutter="20" class="stats-cards">
              <el-col :span="6">
                <el-card class="stat-card requested">
                  <div class="stat-content">
                    <div class="stat-value">{{ censusData.today?.requested?.count || 0 }}</div>
                    <div class="stat-amount">¥{{ censusData.today?.requested?.amount || '0.00' }}</div>
                    <div class="stat-label">今日申请</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card class="stat-card completed">
                  <div class="stat-content">
                    <div class="stat-value">{{ censusData.today?.completed?.count || 0 }}</div>
                    <div class="stat-amount">¥{{ censusData.today?.completed?.amount || '0.00' }}</div>
                    <div class="stat-label">今日到账</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card class="stat-card audit">
                  <div class="stat-content">
                    <div class="stat-value">{{ censusData.today?.auditApproved || 0 }}</div>
                    <div class="stat-label">今日审核通过</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card class="stat-card fee">
                  <div class="stat-content">
                    <div class="stat-value">¥{{ censusData.today?.feeAmount || '0.00' }}</div>
                    <div class="stat-label">今日手续费</div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </el-tab-pane>

          <el-tab-pane label="本月统计" name="month">
            <el-row :gutter="20" class="stats-cards">
              <el-col :span="6">
                <el-card class="stat-card requested">
                  <div class="stat-content">
                    <div class="stat-value">{{ censusData.month?.requested?.count || 0 }}</div>
                    <div class="stat-amount">¥{{ censusData.month?.requested?.amount || '0.00' }}</div>
                    <div class="stat-label">本月申请</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card class="stat-card completed">
                  <div class="stat-content">
                    <div class="stat-value">{{ censusData.month?.completed?.count || 0 }}</div>
                    <div class="stat-amount">¥{{ censusData.month?.completed?.amount || '0.00' }}</div>
                    <div class="stat-label">本月到账</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card class="stat-card audit">
                  <div class="stat-content">
                    <div class="stat-value">{{ censusData.month?.auditApproved || 0 }}</div>
                    <div class="stat-label">本月审核通过</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card class="stat-card fee">
                  <div class="stat-content">
                    <div class="stat-value">¥{{ censusData.month?.feeAmount || '0.00' }}</div>
                    <div class="stat-label">本月手续费</div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </el-tab-pane>

          <el-tab-pane label="到账分布" name="distribution">
            <el-row :gutter="20" class="stats-cards">
              <el-col :span="8">
                <el-card class="stat-card online">
                  <div class="stat-content">
                    <div class="stat-value">{{ censusData.distribution?.onlineCompleted?.count || 0 }}</div>
                    <div class="stat-amount">¥{{ censusData.distribution?.onlineCompleted?.amount || '0.00' }}</div>
                    <div class="stat-label">线上到账</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card class="stat-card offline">
                  <div class="stat-content">
                    <div class="stat-value">{{ censusData.distribution?.offlineCompleted?.count || 0 }}</div>
                    <div class="stat-amount">¥{{ censusData.distribution?.offlineCompleted?.amount || '0.00' }}</div>
                    <div class="stat-label">线下到账</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card class="stat-card balance">
                  <div class="stat-content">
                    <div class="stat-value">¥{{ operatorBalance || '0.00' }}</div>
                    <div class="stat-label">运营账户余额</div>
                  </div>
                </el-card>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="stats-cards mt-20">
              <el-col :span="8">
                <el-card class="stat-card wechat">
                  <div class="stat-content">
                    <div class="stat-value">{{ censusData.distribution?.wechatCompleted?.count || 0 }}</div>
                    <div class="stat-amount">¥{{ censusData.distribution?.wechatCompleted?.amount || '0.00' }}</div>
                    <div class="stat-label">微信到账</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card class="stat-card alipay">
                  <div class="stat-content">
                    <div class="stat-value">{{ censusData.distribution?.alipayCompleted?.count || 0 }}</div>
                    <div class="stat-amount">¥{{ censusData.distribution?.alipayCompleted?.amount || '0.00' }}</div>
                    <div class="stat-label">支付宝到账</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card class="stat-card bank">
                  <div class="stat-content">
                    <div class="stat-value">{{ censusData.distribution?.bankCompleted?.count || 0 }}</div>
                    <div class="stat-amount">¥{{ censusData.distribution?.bankCompleted?.amount || '0.00' }}</div>
                    <div class="stat-label">银行卡到账</div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </div>

      <div class="search-form-container">
        <el-form ref="searchFormRef" :model="searchForm" :inline="true" class="search-form">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="提现单号" prop="code">
                <el-input size="default" v-model="searchForm.code" placeholder="请输入提现单号" clearable
                  style="width: 200px" />
              </el-form-item>

              <el-form-item label="用户ID" prop="userId">
                <el-input size="default" v-model="searchForm.userId" placeholder="请输入用户ID" clearable
                  style="width: 150px" />
              </el-form-item>

              <el-form-item label="师傅ID" prop="coachId">
                <el-input size="default" v-model="searchForm.coachId" placeholder="请输入师傅ID" clearable
                  style="width: 150px" />
              </el-form-item>

              <el-form-item label="状态" prop="status">
                <el-select size="default" v-model="searchForm.status" placeholder="请选择状态" clearable
                  style="width: 180px">
                  <el-option label="内部错误" :value="-1" />
                  <el-option label="已提现，未领取" :value="1" />
                  <el-option label="到账" :value="2" />
                  <el-option label="失败" :value="3" />
                  <el-option label="关闭" :value="4" />
                </el-select>
              </el-form-item>

              <el-form-item label="提现类型" prop="type">
                <el-select size="default" v-model="searchForm.type" placeholder="请选择提现类型" clearable
                  style="width: 150px">
                  <el-option label="车费" :value="1" />
                  <el-option label="服务费" :value="2" />
                  <el-option label="加盟" :value="3" />
                  <el-option label="用户分销" :value="4" />
                </el-select>
              </el-form-item>

              <el-form-item label="付款方式" prop="online">
                <el-select size="default" v-model="searchForm.online" placeholder="请选择付款方式" clearable
                  style="width: 120px">
                  <el-option label="线下" :value="0" />
                  <el-option label="线上" :value="1" />
                </el-select>
              </el-form-item>

              <el-form-item label="来源" prop="sourceType">
                <el-select size="default" v-model="searchForm.sourceType" placeholder="请选择来源" clearable
                  style="width: 120px">
                  <el-option label="APP" :value="1" />
                  <el-option label="小程序" :value="2" />
                </el-select>
              </el-form-item>

              <el-form-item label="提现到" prop="cashToType">
                <el-select size="default" v-model="searchForm.cashToType" placeholder="请选择提现到" clearable
                  style="width: 120px">
                  <el-option label="微信" :value="1" />
                  <el-option label="支付宝" :value="2" />
                  <el-option label="银行卡" :value="3" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="申请时间" prop="timeRange">
                <el-date-picker size="default" v-model="timeRange" type="datetimerange" range-separator="至"
                  start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 350px" />
              </el-form-item>

              <el-form-item>
                <LbButton size="default" type="primary" @click="handleSearch">
                  搜索
                </LbButton>
                <LbButton size="default" @click="handleReset">
                  重置
                </LbButton>
                <LbButton size="default" type="success" icon="Download" @click="handleExport" :loading="exportLoading">
                  导出Excel
                </LbButton>
                <LbButton size="default" type="warning" @click="handleBalanceManage">
                  余额管理
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <div class="table-container">
        <el-table v-loading="loading" :data="tableData" :header-cell-style="{
          background: '#f5f7fa',
          color: '#606266',
          fontSize: '16px',
          fontWeight: '600'
        }" :cell-style="{
            fontSize: '14px',
            padding: '12px 8px'
          }" style="width: 100%">
          <el-table-column prop="id" label="ID" width="80" align="center" />

          <el-table-column prop="code" label="提现单号" min-width="200" />

          <el-table-column label="用户信息" min-width="120">
            <template #default="scope">
              <div>用户ID: {{ scope.row.userId }}</div>
              <div v-if="scope.row.coachId">师傅ID: {{ scope.row.coachId }}</div>
            </template>
          </el-table-column>

          <el-table-column label="金额信息" min-width="150">
            <template #default="scope">
              <div class="amount-info">
                <div class="apply-amount">申请: ¥{{ scope.row.applyPrice || 0 }}</div>
                <div class="service-fee">手续费: ¥{{ scope.row.servicePrice || 0 }}</div>
                <div class="true-amount">实际: ¥{{ scope.row.truePrice || 0 }}</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="120" align="center">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="类型信息" min-width="120">
            <template #default="scope">
              <div>{{ getTypeText(scope.row.type) }}</div>
              <div class="type-detail">
                {{ getSourceText(scope.row.sourceType) }} |
                {{ getCashToText(scope.row.cashToType) }}
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="createTime" label="创建时间" width="160" />

          <el-table-column label="审核状态" width="100" align="center">
            <template #default="scope">
              <el-tag :type="getLockType(scope.row.lock)">
                {{ getLockText(scope.row.lock) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="失败原因" min-width="150">
            <template #default="scope">
              <div v-if="scope.row.friendlyFailReason" class="fail-reason">
                {{ scope.row.friendlyFailReason }}
              </div>
              <div v-else-if="scope.row.text && scope.row.text !== 'SUCCESS'" class="fail-reason">
                {{ scope.row.text }}
              </div>
              <div v-else class="success-text">正常</div>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" fixed="right">
            <template #default="scope">
              <div class="table-operate">
                <LbButton size="mini" type="primary" @click="handleViewDetail(scope.row)">
                  查看详情
                </LbButton>
                <LbButton v-if="scope.row.lock === 0" size="mini" type="success" @click="handleAudit(scope.row, 1)">
                  审核通过
                </LbButton>
                <LbButton v-if="scope.row.lock === 0" size="mini" type="danger" @click="handleAudit(scope.row, 2)">
                  审核拒绝
                </LbButton>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <LbPage :page="searchForm.pageNum" :page-size="searchForm.pageSize" :total="total"
        @handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange" />
    </div>

    <el-dialog v-model="balanceDialogVisible" title="运营账户余额管理" width="500px" :close-on-click-modal="false">
      <el-form ref="balanceFormRef" :model="balanceForm" :rules="balanceRules" label-width="120px">
        <el-form-item label="当前余额">
          <span class="current-balance">¥{{ operatorBalance || '0.00' }}</span>
        </el-form-item>

        <el-form-item label="操作类型" prop="operationType">
          <el-radio-group v-model="balanceForm.operationType">
            <el-radio label="set">设置余额</el-radio>
            <el-radio label="increase">增加余额</el-radio>
            <el-radio label="decrease">扣减余额</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="金额" prop="amount">
          <el-input v-model="balanceForm.amount" placeholder="请输入金额" type="number" step="0.01">
            <template #prepend>¥</template>
          </el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <LbButton @click="balanceDialogVisible = false">取消</LbButton>
          <LbButton type="primary" @click="handleBalanceSubmit">确定</LbButton>
        </span>
      </template>
    </el-dialog>

    <!-- 详情查看对话框 -->
    <el-dialog v-model="detailDialogVisible" title="提现详情" width="700px" :close-on-click-modal="false">
      <div v-if="currentDetail" class="detail-content">
        <div class="detail-section">
          <h4>基本信息</h4>
          <div class="detail-item">
            <label>提现ID：</label>
            <span>{{ currentDetail.id }}</span>
          </div>
          <div class="detail-item">
            <label>提现单号：</label>
            <span>{{ currentDetail.code }}</span>
          </div>
          <div class="detail-item">
            <label>用户ID：</label>
            <span>{{ currentDetail.userId }}</span>
          </div>
          <div class="detail-item">
            <label>师傅ID：</label>
            <span>{{ currentDetail.coachId || '无' }}</span>
          </div>
        </div>

        <div class="detail-section">
          <h4>金额信息</h4>
          <div class="detail-item">
            <label>申请金额：</label>
            <span class="amount apply-amount">¥{{ currentDetail.applyPrice || '0.00' }}</span>
          </div>
          <div class="detail-item">
            <label>手续费：</label>
            <span class="amount service-fee">¥{{ currentDetail.servicePrice || '0.00' }}</span>
          </div>
          <div class="detail-item">
            <label>实际到账：</label>
            <span class="amount true-amount">¥{{ currentDetail.truePrice || '0.00' }}</span>
          </div>
        </div>

        <div class="detail-section">
          <h4>状态信息</h4>
          <div class="detail-item">
            <label>提现状态：</label>
            <el-tag :type="getStatusType(currentDetail.status)">
              {{ getStatusText(currentDetail.status) }}
            </el-tag>
          </div>
          <div class="detail-item">
            <label>审核状态：</label>
            <el-tag :type="getLockType(currentDetail.lock)">
              {{ getLockText(currentDetail.lock) }}
            </el-tag>
          </div>
          <div class="detail-item">
            <label>失败原因：</label>
            <span v-if="currentDetail.friendlyFailReason" class="fail-reason">
              {{ currentDetail.friendlyFailReason }}
            </span>
            <span v-else-if="currentDetail.text && currentDetail.text !== 'SUCCESS'" class="fail-reason">
              {{ currentDetail.text }}
            </span>
            <span v-else class="success-text">正常</span>
          </div>
        </div>

        <div class="detail-section">
          <h4>类型信息</h4>
          <div class="detail-item">
            <label>提现类型：</label>
            <span>{{ getTypeText(currentDetail.type) }}</span>
          </div>
          <div class="detail-item">
            <label>来源平台：</label>
            <span>{{ getSourceText(currentDetail.sourceType) }}</span>
          </div>
          <div class="detail-item">
            <label>提现到：</label>
            <span>{{ getCashToText(currentDetail.cashToType) }}</span>
          </div>
          <div class="detail-item">
            <label>付款方式：</label>
            <span>{{ currentDetail.online === 1 ? '线上' : '线下' }}</span>
          </div>
        </div>

        <div class="detail-section">
          <h4>时间信息</h4>
          <div class="detail-item">
            <label>申请时间：</label>
            <span>{{ currentDetail.createTime }}</span>
          </div>
          <div class="detail-item">
            <label>更新时间：</label>
            <span>{{ currentDetail.updateTime || '无' }}</span>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <LbButton @click="detailDialogVisible = false">关闭</LbButton>
        </span>
      </template>
    </el-dialog>

    <el-dialog v-model="rejectDialogVisible" title="审核拒绝" width="500px" :close-on-click-modal="false">
      <el-form ref="rejectFormRef" :model="rejectForm" :rules="rejectRules" label-width="100px">
        <el-form-item label="拒绝原因" prop="text">
          <el-input v-model="rejectForm.text" type="textarea" :rows="4" placeholder="请输入拒绝原因（非必填）" maxlength="200"
            show-word-limit />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <LbButton @click="rejectDialogVisible = false">取消</LbButton>
          <LbButton type="danger" @click="handleRejectSubmit">确认拒绝</LbButton>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, getCurrentInstance } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbPage from '@/components/common/LbPage.vue'

// 获取当前实例以访问全局属性
const { proxy } = getCurrentInstance()

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const tableData = ref([])
const total = ref(0)
const searchFormRef = ref()
const balanceFormRef = ref()
const rejectFormRef = ref() // Ref for the reject form
const timeRange = ref([])
const balanceDialogVisible = ref(false)
const rejectDialogVisible = ref(false) // Control for reject dialog visibility
const detailDialogVisible = ref(false) // Control for detail dialog visibility
const operatorBalance = ref('0.00')
const currentRejectRow = ref(null) // Stores the row data for the current rejection operation
const currentDetail = ref(null) // Stores the current detail data
const activeStatsTab = ref('overall') // 当前激活的统计标签

// 新的统计数据结构
const censusData = reactive({
  overall: {
    requested: { amount: 0, count: 0 },
    completed: { amount: 0, count: 0 },
    pending: { amount: 0, count: 0 },
    failed: { amount: 0, count: 0 },
    feeAmount: 0,
    auditApproved: 0,
    auditRejected: 0,
    auditPending: 0,
    avgReviewMinutes: null
  },
  today: {
    requested: { amount: 0, count: 0 },
    completed: { amount: 0, count: 0 },
    feeAmount: 0,
    auditApproved: 0,
    auditRejected: 0,
    auditPending: 0,
    avgReviewMinutes: null
  },
  month: {
    requested: { amount: 0, count: 0 },
    completed: { amount: 0, count: 0 },
    feeAmount: 0,
    auditApproved: 0,
    auditRejected: 0,
    auditPending: 0,
    avgReviewMinutes: null
  },
  distribution: {
    onlineCompleted: { amount: 0, count: 0 },
    offlineCompleted: { amount: 0, count: 0 },
    wechatCompleted: { amount: 0, count: 0 },
    alipayCompleted: { amount: 0, count: 0 },
    bankCompleted: { amount: 0, count: 0 }
  }
})

// 旧的统计数据（保留兼容性）
const stats = reactive({
  totalCount: 0,
  totalApply: 0,
  totalService: 0,
  totalArrived: 0,
  withdrawedCount: 0,
  arrivedCount: 0,
  failedCount: 0
})

// 搜索表单
const searchForm = reactive({
  code: '',
  userId: '',
  coachId: '',
  status: '',
  type: '',
  online: '',
  sourceType: '',
  cashToType: '',
  startTime: '',
  endTime: '',
  pageNum: 1,
  pageSize: 10
})

// 余额管理表单
const balanceForm = reactive({
  operationType: 'set',
  amount: ''
})

// 余额管理表单验证规则
const balanceRules = {
  operationType: [
    { required: true, message: '请选择操作类型', trigger: 'change' }
  ],
  amount: [
    { required: true, message: '请输入金额', trigger: 'blur' },
    { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
  ]
}

// 审核拒绝表单
const rejectForm = reactive({
  text: ''
})

// 审核拒绝表单验证规则 (text is optional, so no 'required' rule here)
const rejectRules = {
  text: [
    { max: 200, message: '拒绝原因最多可输入200个字符', trigger: 'blur' }
  ]
}

// 监听时间范围变化
watch(timeRange, (newVal) => {
  if (newVal && newVal.length === 2) {
    searchForm.startTime = newVal[0]
    searchForm.endTime = newVal[1]
  } else {
    searchForm.startTime = ''
    searchForm.endTime = ''
  }
})

// 获取列表数据
const getTableDataList = async () => {
  loading.value = true
  try {
    console.log('🔍 获取提现管理列表，参数:', searchForm)

    // 构建查询参数，过滤空值
    const params = {}
    Object.keys(searchForm).forEach(key => {
      if (searchForm[key] !== '' && searchForm[key] !== null && searchForm[key] !== undefined) {
        params[key] = searchForm[key]
      }
    })

    const result = await proxy.$api.finance.walletList(params)

    if (result.code === '200') {
      tableData.value = result.data.list || []
      total.value = result.data.totalCount || 0
      console.log('✅ 获取提现管理列表成功:', tableData.value.length, '条记录')
    } else {
      console.error('❌ 获取提现管理列表失败:', result.data.msg)
      ElMessage.error(result.data.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('❌ 获取提现管理列表异常:', error)
    ElMessage.error('获取数据失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 获取统计数据 - 使用新的census接口
const getCensusData = async () => {
  try {
    console.log('📊 获取提现统计数据')

    const result = await proxy.$api.finance.walletCensus()

    if (result.code === '200') {
      // 更新census数据
      Object.assign(censusData, result.data)
      console.log('✅ 获取提现统计数据成功:', censusData)
    } else {
      console.error('❌ 获取提现统计数据失败:', result.msg)
      ElMessage.error(result.msg || '获取统计数据失败')
    }
  } catch (error) {
    console.error('❌ 获取提现统计数据异常:', error)
    ElMessage.error('获取统计数据失败，请稍后重试')
  }
}

// 保留旧的统计函数以兼容现有代码
const getStatsData = async () => {
  try {
    console.log('📊 获取提现汇总统计')

    // 构建查询参数，过滤空值
    const params = {}
    Object.keys(searchForm).forEach(key => {
      if (searchForm[key] !== '' && searchForm[key] !== null && searchForm[key] !== undefined && key !== 'pageNum' && key !== 'pageSize') {
        params[key] = searchForm[key]
      }
    })

    const result = await proxy.$api.finance.walletStats(params)

    if (result.code === '200') {
      Object.assign(stats, result.data)
      console.log('✅ 获取提现汇总统计成功:', stats)
    } else {
      console.error('❌ 获取提现汇总统计失败:', result.msg)
    }
  } catch (error) {
    console.error('❌ 获取提现汇总统计异常:', error)
  }
}

// 获取运营账户余额
const getOperatorBalance = async () => {
  try {
    console.log('💰 获取运营账户余额')

    const result = await proxy.$api.finance.operatorBalance()

    if (result.code === '200') {
      operatorBalance.value = result.data || '0.00'
      console.log('✅ 获取运营账户余额成功:', operatorBalance.value)
    } else {
      console.error('❌ 获取运营账户余额失败:', result.msg)
    }
  } catch (error) {
    console.error('❌ 获取运营账户余额异常:', error)
  }
}

// 搜索
const handleSearch = () => {
  console.log('🔍 执行搜索，参数:', searchForm)
  searchForm.pageNum = 1
  getTableDataList()
  getCensusData()
}

// 重置
const handleReset = () => {
  console.log('🔄 重置搜索条件')

  // 重置搜索表单
  Object.keys(searchForm).forEach(key => {
    if (key === 'pageNum') {
      searchForm[key] = 1
    } else if (key === 'pageSize') {
      searchForm[key] = 10
    } else {
      searchForm[key] = ''
    }
  })

  // 重置时间范围
  timeRange.value = []

  // 重新获取数据
  getTableDataList()
  getCensusData()
}

/**
 * 导出提现管理Excel - 参考FinanceList.vue实现，使用POST方法
 */
const handleExport = async () => {
  try {
    exportLoading.value = true
    console.log('📤 开始导出提现管理Excel...')

    // 构建导出参数JSON对象
    const exportParams = {}

    // 添加用户ID
    if (searchForm.userId !== '' && searchForm.userId !== null && searchForm.userId !== undefined) {
      exportParams.userId = parseInt(searchForm.userId)
    }

    // 添加师傅ID
    if (searchForm.coachId !== '' && searchForm.coachId !== null && searchForm.coachId !== undefined) {
      exportParams.coachId = parseInt(searchForm.coachId)
    }

    // 添加状态
    if (searchForm.status !== '' && searchForm.status !== null && searchForm.status !== undefined) {
      exportParams.status = parseInt(searchForm.status)
    }

    // 添加类型
    if (searchForm.type !== '' && searchForm.type !== null && searchForm.type !== undefined) {
      exportParams.type = parseInt(searchForm.type)
    }

    // 添加时间范围
    if (searchForm.startTime !== '' && searchForm.startTime !== null && searchForm.startTime !== undefined) {
      exportParams.startTime = searchForm.startTime
    }

    if (searchForm.endTime !== '' && searchForm.endTime !== null && searchForm.endTime !== undefined) {
      exportParams.endTime = searchForm.endTime
    }

    console.log('📤 导出参数:', exportParams)

    // 使用fetch发送POST请求下载文件
    const token = sessionStorage.getItem('minitk')
    const baseUrl = import.meta.env.VITE_API_BASE_URL || ''
    const exportUrl = `${baseUrl}/api/admin/wallet/export`
    const response = await fetch(exportUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` })
      },
      body: JSON.stringify(exportParams)
    })

    if (response.ok) {
      // 检查响应内容类型
      const contentType = response.headers.get('Content-Type')

      // 如果是JSON响应，说明可能是错误信息
      if (contentType && contentType.includes('application/json')) {
        const errorData = await response.json()
        console.error('❌ 导出返回错误:', errorData)

        if (errorData.code === '-1' || errorData.code === -1) {
          // 显示具体的错误信息
          const errorMsg = errorData.msg || '导出失败'
          ElMessage.error(`导出失败: ${errorMsg}`)

          // 如果是数据库字段映射错误，给出更友好的提示
          if (errorMsg.includes('ResultMapException') || errorMsg.includes('column')) {
            ElMessage.warning('后端数据库字段映射异常，请联系技术人员修复')
          }
        } else {
          ElMessage.error(errorData.msg || '导出失败')
        }
        return
      }

      // 获取文件名（从响应头或使用默认名称）
      const contentDisposition = response.headers.get('Content-Disposition')
      let filename = `提现管理导出_${new Date().toISOString().slice(0, 10)}.xlsx`

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1].replace(/['"]/g, '')
        }
      }

      // 创建Blob对象并下载
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      link.style.display = 'none'

      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // 清理URL对象
      window.URL.revokeObjectURL(url)

      ElMessage.success('导出成功，请查看浏览器下载')
      console.log('✅ 导出提现管理Excel成功')
    } else {
      // 尝试解析错误响应
      try {
        const errorText = await response.text()
        console.error('❌ 导出HTTP错误:', response.status, response.statusText, errorText)

        // 尝试解析JSON错误信息
        try {
          const errorData = JSON.parse(errorText)
          if (errorData.msg) {
            ElMessage.error(`导出失败: ${errorData.msg}`)
          } else {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
          }
        } catch (parseError) {
          throw new Error(`导出失败: HTTP ${response.status} ${response.statusText}`)
        }
      } catch (textError) {
        throw new Error(`导出失败: HTTP ${response.status} ${response.statusText}`)
      }
    }

  } catch (error) {
    console.error('❌ 导出提现管理Excel异常:', error)
    ElMessage.error('导出失败，请稍后重试')
  } finally {
    exportLoading.value = false
  }
}

// 查看详情
const handleViewDetail = (row) => {
  console.log('👁️ 查看提现详情:', row)
  currentDetail.value = row
  detailDialogVisible.value = true
}

// 审核提现
const handleAudit = async (row, auditStatus) => {
  if (auditStatus === 2) {
    // If "审核拒绝", open the dialog
    currentRejectRow.value = row
    rejectForm.text = '' // Clear previous rejection reason
    rejectDialogVisible.value = true
  } else {
    // For "审核通过", proceed with direct confirmation
    const auditText = auditStatus === 1 ? '审核通过' : '审核拒绝' // This will only be '审核通过' here

    try {
      await ElMessageBox.confirm(
        `确定要${auditText}这条提现记录吗？`,
        '确认操作',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      console.log(`✅ ${auditText}提现记录:`, row.id)

      const result = await proxy.$api.finance.walletAudit({
        id: row.id,
        lock: auditStatus,
        adminId: 1 // 这里应该从用户信息中获取管理员ID
      })

      if (result.code === '200') {
        ElMessage.success(`${auditText}成功`)
        console.log(`✅ ${auditText}提现记录成功`)

        // 刷新列表
        getTableDataList()
        getCensusData()
      } else {
        console.error(`❌ ${auditText}提现记录失败:`, result.msg)
        ElMessage.error(result.msg || `${auditText}失败`)
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error(`❌ ${auditText}提现记录异常:`, error)
        ElMessage.error(`${auditText}失败，请稍后重试`)
      }
    }
  }
}

// 页面挂载时初始化数据
onMounted(() => {
  console.log('🚀 提现管理页面初始化')
  getTableDataList()
  getCensusData()
  getOperatorBalance()
})

// 页面大小变化
const handleSizeChange = (size) => {
  console.log('📄 每页数量变化:', size)
  searchForm.pageSize = size
  searchForm.pageNum = 1
  getTableDataList()
}

// 页码变化
const handleCurrentChange = (page) => {
  console.log('📄 页码变化:', page)
  searchForm.pageNum = page
  getTableDataList()
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    '-1': 'danger',
    '1': 'warning',
    '2': 'success',
    '3': 'danger',
    '4': 'info'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    '-1': '内部错误',
    '1': '已提现，未领取',
    '2': '到账',
    '3': '失败',
    '4': '关闭'
  }
  return statusMap[status] || '未知'
}

// 获取审核状态类型
const getLockType = (lock) => {
  const lockMap = {
    '0': 'warning',
    '1': 'success',
    '2': 'danger'
  }
  return lockMap[lock] || 'info'
}

// 获取审核状态文本
const getLockText = (lock) => {
  const lockMap = {
    '0': '待审核',
    '1': '已通过',
    '2': '已拒绝'
  }
  return lockMap[lock] || '未知'
}

// 获取类型文本
const getTypeText = (type) => {
  const typeMap = {
    '1': '车费',
    '2': '服务费',
    '3': '加盟',
    '4': '用户分销'
  }
  return typeMap[type] || '未知'
}

// 获取来源文本
const getSourceText = (sourceType) => {
  const sourceMap = {
    '1': 'APP',
    '2': '小程序'
  }
  return sourceMap[sourceType] || '未知'
}

// 获取提现到文本
const getCashToText = (cashToType) => {
  const cashToMap = {
    '1': '微信',
    '2': '支付宝',
    '3': '银行卡'
  }
  return cashToMap[cashToType] || '未知'
}

// 提交审核拒绝
const handleRejectSubmit = async () => {
  try {
    await rejectFormRef.value.validate() // Validate the rejection form

    if (!currentRejectRow.value) {
      ElMessage.error('没有选中要拒绝的提现记录')
      return
    }

    console.log('🚫 提交审核拒绝:', currentRejectRow.value.id, '原因:', rejectForm.text)

    const result = await proxy.$api.finance.walletAudit({
      id: currentRejectRow.value.id,
      lock: 2, // 审核拒绝状态
      adminId: 1, // 这里应该从用户信息中获取管理员ID
      text: rejectForm.text // 拒绝原因
    })

    if (result.code === '200') {
      ElMessage.success('审核拒绝成功')
      console.log('✅ 审核拒绝提现记录成功')

      // 关闭对话框
      rejectDialogVisible.value = false

      // 刷新列表
      getTableDataList()
      getStatsData()
    } else {
      console.error('❌ 审核拒绝提现记录失败:', result.msg)
      ElMessage.error(result.msg || '审核拒绝失败')
    }
  } catch (error) {
    // Catch validation errors or other issues
    if (error === 'cancel') {
      console.log('审核拒绝操作取消')
    } else {
      console.error('❌ 提交审核拒绝异常:', error)
      ElMessage.error('审核拒绝失败，请稍后重试')
    }
  }
}


// 余额管理
const handleBalanceManage = () => {
  console.log('💰 打开余额管理对话框')
  balanceDialogVisible.value = true

  // 重置表单
  balanceForm.operationType = 'set'
  balanceForm.amount = ''

  // 刷新余额
  getOperatorBalance()
}

// 提交余额管理
const handleBalanceSubmit = async () => {
  try {
    // 表单验证
    await balanceFormRef.value.validate()

    console.log('💰 提交余额管理:', balanceForm)

    let result
    const params = { amount: balanceForm.amount }

    switch (balanceForm.operationType) {
      case 'set':
        result = await proxy.$api.finance.operatorBalanceSet(params)
        break
      case 'increase':
        result = await proxy.$api.finance.operatorBalanceIncrease(params)
        break
      case 'decrease':
        result = await proxy.$api.finance.operatorBalanceDecrease(params)
        break
      default:
        throw new Error('无效的操作类型')
    }

    if (result.code === '200') {
      ElMessage.success('操作成功')
      console.log('✅ 余额管理操作成功')

      // 关闭对话框
      balanceDialogVisible.value = false

      // 刷新余额
      getOperatorBalance()
    } else {
      console.error('❌ 余额管理操作失败:', result.msg)
      ElMessage.error(result.msg || '操作失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('❌ 余额管理操作异常:', error)
      ElMessage.error('操作失败，请稍后重试')
    }
  }
}




// 生命周期
onMounted(() => {
  console.log('🚀 提现管理页面初始化')
  getTableDataList()
  getCensusData()
  getOperatorBalance()
})
</script>

<style scoped>
/* 主容器样式 */
.service-withdraw {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 内容容器样式 */
.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 统计概览样式 */
.stats-overview {
  margin-bottom: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.stats-tabs {
  padding: 0;
}

.stats-tabs :deep(.el-tabs__header) {
  margin: 0;
  background: #f8f9fa;
  padding: 0 20px;
}

.stats-tabs :deep(.el-tabs__nav-wrap) {
  padding: 0;
}

.stats-tabs :deep(.el-tabs__content) {
  padding: 20px;
}

/* 统计卡片样式 */
.stats-cards {
  margin-bottom: 0;
}

.stats-cards.mt-20 {
  margin-top: 20px;
}

.stat-card {
  border: none;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.stat-card :deep(.el-card__body) {
  padding: 20px;
  position: relative;
}

/* 统计内容布局 */
.stat-content {
  text-align: center;
  position: relative;
  z-index: 2;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 8px;
  line-height: 1;
}

.stat-amount {
  font-size: 18px;
  font-weight: 600;
  color: #606266;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  font-weight: 500;
}

/* 统一的黑白配色主题 */
.stat-card.requested,
.stat-card.completed,
.stat-card.pending,
.stat-card.failed,
.stat-card.audit,
.stat-card.fee,
.stat-card.review-time,
.stat-card.online,
.stat-card.offline,
.stat-card.balance,
.stat-card.wechat,
.stat-card.alipay,
.stat-card.bank {
  background: #ffffff;
  border: 1px solid #e4e7ed;
  color: #303133;
}

.stat-card.requested .stat-value,
.stat-card.completed .stat-value,
.stat-card.pending .stat-value,
.stat-card.failed .stat-value,
.stat-card.audit .stat-value,
.stat-card.fee .stat-value,
.stat-card.review-time .stat-value,
.stat-card.online .stat-value,
.stat-card.offline .stat-value,
.stat-card.balance .stat-value,
.stat-card.wechat .stat-value,
.stat-card.alipay .stat-value,
.stat-card.bank .stat-value {
  color: #303133;
  font-weight: 700;
}

.stat-card.requested .stat-amount,
.stat-card.completed .stat-amount,
.stat-card.pending .stat-amount,
.stat-card.failed .stat-amount,
.stat-card.audit .stat-amount,
.stat-card.fee .stat-amount,
.stat-card.review-time .stat-amount,
.stat-card.online .stat-amount,
.stat-card.offline .stat-amount,
.stat-card.balance .stat-amount,
.stat-card.wechat .stat-amount,
.stat-card.alipay .stat-amount,
.stat-card.bank .stat-amount {
  color: #606266;
  font-weight: 600;
}

.stat-card.requested .stat-label,
.stat-card.completed .stat-label,
.stat-card.pending .stat-label,
.stat-card.failed .stat-label,
.stat-card.audit .stat-label,
.stat-card.fee .stat-label,
.stat-card.review-time .stat-label,
.stat-card.online .stat-label,
.stat-card.offline .stat-label,
.stat-card.balance .stat-label,
.stat-card.wechat .stat-label,
.stat-card.alipay .stat-label,
.stat-card.bank .stat-label {
  color: #909399;
  font-weight: 500;
}

/* 悬停效果 */
.stat-card:hover {
  background: #f8f9fa;
  border-color: #c0c4cc;
}

/* 审核统计特殊样式 */
.audit-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 12px;
}

.audit-item {
  text-align: center;
}

.audit-label {
  font-size: 12px;
  color: #666;
  display: block;
  margin-bottom: 4px;
}

.audit-value {
  font-size: 20px;
  font-weight: 600;
  display: block;
}

.audit-value.approved {
  color: #67c23a;
}

.audit-value.rejected {
  color: #f56c6c;
}

.audit-value.pending {
  color: #e6a23c;
}

/* 搜索表单容器样式 */
.search-form-container {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

/* 表格容器样式 */
.table-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 20px;
}

/* 表格内容样式 */
.amount-info {
  line-height: 1.4;
}

.apply-amount {
  color: #e6a23c;
  font-weight: 600;
}

.service-fee {
  color: #909399;
  font-size: 12px;
}

.true-amount {
  color: #67c23a;
  font-weight: 600;
}

.type-detail {
  color: #909399;
  font-size: 12px;
  margin-top: 2px;
}

.fail-reason {
  color: #f56c6c;
  font-size: 12px;
  max-width: 150px;
  word-break: break-all;
}

.success-text {
  color: #67c23a;
  font-size: 12px;
}

/* 操作按钮样式 */
.table-operate {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.table-operate .el-button {
  margin: 0;
}

/* 余额管理对话框样式 */
.current-balance {
  font-size: 18px;
  font-weight: 600;
  color: #67c23a;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 深度选择器覆盖Element Plus样式 */
:deep(.el-table) {
  border: none;
}

:deep(.el-table__header-wrapper) {
  border-radius: 8px 8px 0 0;
}

:deep(.el-table__body-wrapper) {
  border-radius: 0 0 8px 8px;
}

:deep(.el-table th) {
  background: #f5f7fa !important;
  color: #606266 !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  padding: 15px 8px !important;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table td) {
  font-size: 14px !important;
  color: #333 !important;
  padding: 12px 8px !important;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table__row:hover) {
  background-color: #f8f9fa !important;
}

:deep(.el-table__row:last-child td) {
  border-bottom: none;
}

/* 详情弹窗样式 */
.detail-content {
  padding: 10px 0;
}

.detail-section {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-section:last-child {
  border-bottom: none;
}

.detail-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
  padding-bottom: 8px;
  border-bottom: 2px solid #409eff;
  display: inline-block;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  padding: 8px 0;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item label {
  font-weight: 600;
  color: #333;
  width: 120px;
  flex-shrink: 0;
  margin-right: 15px;
}

.detail-item span {
  color: #666;
  flex: 1;
}

.detail-item .amount {
  font-weight: 600;
  font-size: 16px;
}

.detail-item .apply-amount {
  color: #e6a23c;
}

.detail-item .service-fee {
  color: #909399;
}

.detail-item .true-amount {
  color: #67c23a;
}

.detail-item .fail-reason {
  color: #f56c6c;
}

.detail-item .success-text {
  color: #67c23a;
}

/* 响应式适配 */
@media (max-width: 1200px) {
  .content-container {
    padding: 15px;
  }

  .stats-cards .el-col {
    margin-bottom: 15px;
  }
}

@media (max-width: 768px) {
  .content-container {
    padding: 10px;
  }

  .search-form-container {
    padding: 12px;
  }

  .stats-value {
    font-size: 24px;
  }

  .stats-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }

  .table-operate {
    flex-direction: column;
  }
}
</style>
```